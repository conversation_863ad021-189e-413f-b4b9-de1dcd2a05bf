// 导出TableHead 数据
export const GoodsTableHead = [
  {
    title: '图片',
    slot: 'pic',
    align: 'center',
    minWidth: '80px',
  },
  {
    title: '售价',
    slot: 'price',
    align: 'center',
    minWidth: '120px',
  },
  {
    title: '成本价',
    slot: 'cost',
    align: 'center',
    minWidth: '120px',
  },
  {
    title: '划线价',
    slot: 'ot_price',
    align: 'center',
    minWidth: '120px',
  },
  {
    title: '库存',
    slot: 'stock',
    align: 'center',
    minWidth: '120px',
  },
  {
    title: '商品编码',
    slot: 'bar_code',
    align: 'center',
    minWidth: '120px',
  },
  {
    title: '条形码',
    slot: 'bar_code_number',
    align: 'center',
    minWidth: '120px',
  },
  {
    title: '重量（KG）',
    slot: 'weight',
    align: 'center',
    minWidth: '95px',
  },
  {
    title: '体积(m³)',
    slot: 'volume',
    align: 'center',
    minWidth: '95px',
  },
  {
    title: '默认选中规格',
    slot: 'selected_spec',
    fixed: 'right',
    align: 'center',
    minWidth: '100px',
  },
  {
    title: '操作',
    slot: 'action',
    fixed: 'right',
    align: 'center',
    minWidth: '120px',
  },
];
//   虚拟商品-卡密 优惠券
export const VirtualTableHead = [
  {
    title: '图片',
    slot: 'pic',
    align: 'center',
    minWidth: 80,
  },
  {
    title: '售价',
    slot: 'price',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '成本价',
    slot: 'cost',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '原价',
    slot: 'ot_price',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '库存',
    slot: 'stock',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '产品编号',
    slot: 'bar_code',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '虚拟商品',
    slot: 'fictitious',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '默认选中规格',
    slot: 'selected_spec',
    fixed: 'right',
    align: 'center',
    minWidth: 90,
  },
  {
    title: '操作',
    slot: 'action',
    fixed: 'right',
    align: 'center',
    minWidth: 120,
  },
];
//   虚拟商品
export const VirtualTableHead2 = [
  {
    title: '图片',
    slot: 'pic',
    align: 'center',
    minWidth: 80,
  },
  {
    title: '售价',
    slot: 'price',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '成本价',
    slot: 'cost',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '原价',
    slot: 'ot_price',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '库存',
    slot: 'stock',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '产品编号',
    slot: 'bar_code',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '默认选中规格',
    slot: 'selected_spec',
    fixed: 'right',
    align: 'center',
    minWidth: 90,
  },
  {
    title: '操作',
    slot: 'action',
    fixed: 'right',
    align: 'center',
    minWidth: 120,
  },
];
