import LayoutMain from '@/layout';
import setting from '@/setting';
let routePre = setting.routePre;

const pre = 'crud_';

export default {
  path: routePre + '/crud',
  name: 'crud',
  header: 'crud',
  redirect: {
    name: `${pre}crud`,
  },
  meta: {
    auth: true,
  },
  component: LayoutMain,
  children: [
    {
      path: ':table_name',
      name: `${pre}crud`,
      meta: {
        auth: true,
        title: '增删改查',
      },
      component: () => import('@/pages/crud/index'),
    },
  ],
};
